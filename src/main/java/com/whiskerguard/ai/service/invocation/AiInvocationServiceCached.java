package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.AiResponseCacheService;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.SensitiveWordFilterService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.exception.AiInvocationException;
import com.whiskerguard.ai.service.mapper.AiRequestMapper;
import com.whiskerguard.ai.service.prompt.PromptBuildRequest;
import com.whiskerguard.ai.service.prompt.PromptBuilderService;
import com.whiskerguard.ai.service.workflow.AiWorkflow;
import com.whiskerguard.ai.service.workflow.AiWorkflowResult;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI 调用主要业务服务 - 缓存增强版
 * <p>
 * 在原有功能基础上添加智能缓存层，显著提升响应性能
 */
@Service
@Transactional
public class AiInvocationServiceCached {

    private static final Logger log = LoggerFactory.getLogger(AiInvocationServiceCached.class);

    private final AiRequestRepository requestRepo;
    private final AiRequestMapper requestMapper;
    private final AiToolMetricsService metricsService;
    private final ObjectMapper objectMapper;
    private final AiToolRepository toolRepo;
    private final WorkflowClient workflowClient;
    private final SensitiveWordFilterService sensitiveWordFilterService;
    private final AiResponseCacheService cacheService;
    private final AiToolRouter toolRouter;
    private final PromptBuilderService promptBuilderService;
    private final BrandIdentityReplacementService brandIdentityReplacementService;

    @Value("${temporal.task-queue:AiTaskQueue}")
    private String taskQueue;

    @Value("${whiskerguard.ai.cache.bypass-workflow:true}")
    private boolean bypassWorkflowForCache;

    public AiInvocationServiceCached(
        AiRequestRepository requestRepo,
        AiRequestMapper requestMapper,
        AiToolMetricsService metricsService,
        ObjectMapper objectMapper,
        AiToolRepository toolRepo,
        WorkflowClient workflowClient,
        SensitiveWordFilterService sensitiveWordFilterService,
        @Autowired(required = false) AiResponseCacheService cacheService,
        AiToolRouter toolRouter,
        PromptBuilderService promptBuilderService,
        BrandIdentityReplacementService brandIdentityReplacementService
    ) {
        this.requestRepo = requestRepo;
        this.requestMapper = requestMapper;
        this.metricsService = metricsService;
        this.objectMapper = objectMapper;
        this.toolRepo = toolRepo;
        this.workflowClient = workflowClient;
        this.sensitiveWordFilterService = sensitiveWordFilterService;
        this.cacheService = cacheService;
        this.toolRouter = toolRouter;
        this.promptBuilderService = promptBuilderService;
        this.brandIdentityReplacementService = brandIdentityReplacementService;
    }

    /**
     * 发起 AI 调用 - 缓存增强版
     * <p>
     * 优化策略：
     * 1. 智能缓存检查
     * 2. 快速路径（绕过Temporal）
     * 3. 异步数据库操作
     * 4. 支持模板化提示词构建
     */
    public AiRequestDTO invoke(AiInvocationRequestDTO dto) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理AI调用请求，工具类型: {}, 员工ID: {}, 使用模板: {}", dto.getToolKey(), dto.getEmployeeId(), dto.isUseTemplate());

        // 1. 参数验证
        validateInvocationRequest(dto);

        // 2. 处理模板化提示词（如果启用）
        AiInvocationRequestDTO processedDto = processTemplateIfNeeded(dto);

        // 3. 查找AI工具配置
        AiTool aiTool = findAiToolByKey(processedDto.getToolKey(), processedDto);
        log.debug("找到AI工具配置: {}", aiTool.getName());

        // 4. 创建请求记录（异步优化）
        AiRequest req = createAiRequest(processedDto, aiTool);
        req = requestRepo.save(req);
        log.debug("已保存AI请求记录，ID: {}", req.getId());

        AiResult result = null;

        try {
            if (cacheService != null && bypassWorkflowForCache) {
                // 5a. 缓存路径 - 快速响应
                log.debug("使用缓存增强的快速路径");
                result = cacheService.getCachedOrCompute(processedDto, () -> {
                    log.debug("缓存未命中，执行AI工具调用");
                    return toolRouter.route(processedDto);
                });

                // 转换为工作流结果格式
                AiWorkflowResult workflowResult = new AiWorkflowResult(result.getContent(), result.getUsage());
                workflowResult.setDurationMs(System.currentTimeMillis() - startTime);

                updateRequestWithSuccess(req, workflowResult);
            } else {
                // 5b. 工作流路径 - 原有逻辑
                log.debug("使用Temporal工作流路径");
                String workflowId = "ai-workflow-" + UUID.randomUUID();
                AiWorkflow workflow = workflowClient.newWorkflowStub(
                    AiWorkflow.class,
                    WorkflowOptions.newBuilder()
                        .setTaskQueue(taskQueue)
                        .setWorkflowId(workflowId)
                        .setWorkflowExecutionTimeout(java.time.Duration.ofMinutes(5))
                        .setWorkflowRunTimeout(java.time.Duration.ofMinutes(4))
                        .build()
                );

                AiWorkflowResult workflowResult = workflow.execute(processedDto);
                updateRequestWithSuccess(req, workflowResult);

                // 如果有缓存服务，手动缓存结果
                if (cacheService != null && workflowResult.getContent() != null) {
                    AiResult cacheResult = AiResult.builder()
                        .content(workflowResult.getContent())
                        .usage(workflowResult.getUsage())
                        .durationMs(workflowResult.getDurationMs())
                        .build();
                    // 注意：这里需要修改缓存服务以支持手动添加
                }
            }

            long totalTime = System.currentTimeMillis() - startTime;
            log.info(
                "AI调用成功完成，总耗时: {}ms，请求ID: {}, 响应长度: {}",
                totalTime,
                req.getId(),
                result != null && result.getContent() != null ? result.getContent().length() : 0
            );

            // 6. 异步保存指标
            saveMetricsAsync(processedDto.getToolKey(), result, req, totalTime);
        } catch (Exception ex) {
            log.error("AI调用失败，请求ID: {}, 错误: {}", req.getId(), ex.getMessage(), ex);
            updateRequestWithError(req, ex);
            throw new AiInvocationException("AI调用失败: " + ex.getMessage(), processedDto.getToolKey(), "EXECUTION_FAILED", ex);
        }

        return requestMapper.toDto(req);
    }

    /**
     * 异步保存指标，不阻塞主流程
     */
    private void saveMetricsAsync(String toolKey, AiResult result, AiRequest req, long duration) {
        try {
            if (result != null) {
                // 构造工作流结果用于指标保存
                AiWorkflowResult workflowResult = new AiWorkflowResult(result.getContent(), result.getUsage());
                workflowResult.setDurationMs(duration);
                metricsService.saveAiCallMetrics(toolKey, workflowResult, req);
                log.debug("已保存AI调用指标");
            }
        } catch (Exception ex) {
            log.warn("保存指标失败，但不影响主流程: {}", ex.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    public AiResponseCacheService.CacheStatistics getCacheStatistics() {
        if (cacheService != null) {
            return cacheService.getCacheStatistics();
        }
        return null;
    }

    /**
     * 手动清除缓存
     */
    public void clearCache() {
        if (cacheService != null) {
            cacheService.clearCache();
            log.info("AI缓存已手动清空");
        }
    }

    /**
     * 处理模板化提示词（如果启用）
     * 如果启用了模板功能，将使用模板构建最终的提示词
     * 如果未启用模板功能，直接返回原始DTO
     */
    private AiInvocationRequestDTO processTemplateIfNeeded(AiInvocationRequestDTO dto) {
        if (!dto.isUseTemplate()) {
            log.debug("未启用模板功能，使用原始提示词");
            return dto;
        }

        try {
            log.info("启用模板功能，开始构建模板化提示词，模板键: {}, 模板类型: {}", dto.getTemplateKey(), dto.getTemplateType());

            // 构建模板请求
            PromptBuildRequest request = PromptBuildRequest.builder()
                .tenantId(dto.getTenantId())
                .variables(dto.getTemplateVariables() != null ? dto.getTemplateVariables() : Map.of())
                .enableRagEnhancement(true) // 默认启用RAG增强
                .useCache(true); // 默认使用缓存

            // 优先使用模板键，其次使用模板类型
            if (dto.getTemplateKey() != null && !dto.getTemplateKey().trim().isEmpty()) {
                request.templateKey(dto.getTemplateKey());
                log.debug("使用模板键: {}", dto.getTemplateKey());
            } else if (dto.getTemplateType() != null) {
                request.templateType(dto.getTemplateType());
                log.debug("使用模板类型: {}", dto.getTemplateType());
            } else {
                log.warn("启用了模板功能但未指定模板键或模板类型，将使用原始提示词");
                return dto;
            }

            request = request.build();

            // 构建模板化提示词
            String templatePrompt = promptBuilderService.buildPrompt(request);
            log.debug("模板化提示词构建成功，长度: {}", templatePrompt.length());

            // 创建新的DTO，使用模板化提示词
            AiInvocationRequestDTO processedDto = new AiInvocationRequestDTO(
                dto.getToolKey(),
                templatePrompt, // 使用模板化提示词
                dto.getMetadata(),
                dto.getTenantId(),
                dto.getEmployeeId(),
                dto.getTemplateKey(),
                dto.getTemplateType(),
                dto.getTemplateVariables(),
                dto.isUseTemplate()
            );

            log.info(
                "模板化提示词处理完成，原始长度: {}, 模板化长度: {}",
                dto.getPrompt() != null ? dto.getPrompt().length() : 0,
                templatePrompt.length()
            );

            return processedDto;
        } catch (Exception e) {
            log.error("模板化提示词构建失败，将使用原始提示词，错误: {}", e.getMessage(), e);
            // 如果模板构建失败，降级到原始提示词
            return dto;
        }
    }

    // 以下方法与原AiInvocationService相同
    private void validateInvocationRequest(AiInvocationRequestDTO dto) {
        if (dto.getToolKey() == null || dto.getToolKey().trim().isEmpty()) {
            throw new IllegalArgumentException("工具类型不能为空");
        }
        if (dto.getPrompt() == null || dto.getPrompt().trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        if (dto.getEmployeeId() == null) {
            throw new IllegalArgumentException("员工ID不能为空");
        }

        // 如果启用了模板功能，验证模板相关参数
        if (dto.isUseTemplate()) {
            if ((dto.getTemplateKey() == null || dto.getTemplateKey().trim().isEmpty()) && dto.getTemplateType() == null) {
                throw new IllegalArgumentException("启用模板功能时，必须指定模板键或模板类型");
            }
        }

        log.debug("请求参数验证通过");
    }

    private AiTool findAiToolByKey(String toolKey, AiInvocationRequestDTO dto) {
        boolean isModel = false;
        if (dto.getMetadata() != null && dto.getMetadata().containsKey("isModel")) {
            Object isModelValue = dto.getMetadata().get("isModel");
            if (isModelValue instanceof Boolean) {
                isModel = (Boolean) isModelValue;
            } else if (isModelValue instanceof String) {
                isModel = Boolean.parseBoolean((String) isModelValue);
            }
            log.debug("从metadata中获取isModel值: {}", isModel);
        }

        if (isModel) {
            List<AiTool> tools = toolRepo.findByToolKeyAndIsDeletedFalse(toolKey);
            if (tools.isEmpty()) {
                throw new AiInvocationException("未找到模型配置: " + toolKey, toolKey, "MODEL_NOT_FOUND");
            }
            // 过滤并获取第一个模型类型的工具
            return tools
                .stream()
                .filter(tool -> tool.getIsModel() != null && tool.getIsModel())
                .findFirst()
                .orElseThrow(() -> new AiInvocationException("未找到模型配置: " + toolKey, toolKey, "MODEL_NOT_FOUND"));
        } else {
            return toolRepo
                .findByToolKeyAndIsDeletedFalseAndIsModelFalse(toolKey)
                .orElseThrow(() -> new AiInvocationException("未找到工具配置: " + toolKey, toolKey, "TOOL_NOT_FOUND"));
        }
    }

    private AiRequest createAiRequest(AiInvocationRequestDTO dto, AiTool aiTool) {
        AiRequest req = new AiRequest();
        req.setToolType(dto.getToolKey());
        req.setPrompt(dto.getPrompt());
        req.setResponse("");
        req.setTool(aiTool);

        Instant now = Instant.now();
        req.setCreatedAt(now);
        req.setUpdatedAt(now);
        req.setRequestTime(now);

        req.setStatus(RequestStatus.PROCESSING);
        req.setVersion(1);

        req.setTenantId(dto.getTenantId());
        req.setEmployeeId(dto.getEmployeeId());
        req.setIsDeleted(false);

        try {
            req.setMetadata(dto.getMetadata() == null ? null : objectMapper.writeValueAsString(dto.getMetadata()));
        } catch (JsonProcessingException e) {
            log.warn("序列化元数据失败: {}", e.getMessage());
            req.setMetadata(null);
        }

        return req;
    }

    private void updateRequestWithSuccess(AiRequest req, AiWorkflowResult result) {
        String content = result.getContent();
        if (content != null && content.length() > 50000) {
            content = content.substring(0, 49997) + "...";
            log.warn("AI响应内容过长，已截断。原长度: {}, 截断后长度: {}", result.getContent().length(), content.length());
        }

        // 品牌身份替换处理
        if (content != null && !content.isEmpty()) {
            try {
                String processedContent = brandIdentityReplacementService.processResponse(content);
                if (!content.equals(processedContent)) {
                    log.debug("AI响应已进行品牌身份替换处理，请求ID: {}", req.getId());
                    content = processedContent;
                }
            } catch (Exception e) {
                log.warn("品牌身份替换处理失败，使用原始内容，请求ID: {}, 错误: {}", req.getId(), e.getMessage());
            }
        }

        if (content != null && !content.isEmpty()) {
            log.debug("对AI响应内容进行敏感词过滤，处理前内容长度: {}", content.length());
            content = sensitiveWordFilterService.filterContent(content);
            log.debug("敏感词过滤完成，处理后内容长度: {}", content.length());
        }

        req.setResponse(content);
        req.setStatus(RequestStatus.SUCCESS);
        req.setResponseTime(Instant.now());
        req.setUpdatedAt(Instant.now());
        requestRepo.save(req);
    }

    private void updateRequestWithError(AiRequest req, Exception e) {
        String errorMessage = e.getMessage();
        if (errorMessage != null && errorMessage.length() > 10000) {
            errorMessage = errorMessage.substring(0, 9997) + "...";
            log.warn("错误信息过长，已截断。原长度: {}, 截断后长度: {}", e.getMessage().length(), errorMessage.length());
        }

        req.setStatus(RequestStatus.FAILED);
        req.setErrorMessage(errorMessage);
        req.setResponseTime(Instant.now());
        req.setUpdatedAt(Instant.now());
        requestRepo.save(req);
    }
}
