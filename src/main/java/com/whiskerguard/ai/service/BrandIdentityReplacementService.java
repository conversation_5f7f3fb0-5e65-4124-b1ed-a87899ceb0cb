package com.whiskerguard.ai.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 品牌身份替换服务
 * <p>
 * 负责将AI模型的自我介绍统一替换为"猫伯伯智能合规管家"，
 * 确保品牌形象的一致性和专业性。
 * 
 * 主要功能：
 * 1. 识别各种AI模型的自我介绍模式
 * 2. 智能替换为统一的品牌身份
 * 3. 保持回答的自然性和连贯性
 * 4. 支持可配置的替换规则
 * 
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
@Service
public class BrandIdentityReplacementService {

    private static final Logger log = LoggerFactory.getLogger(BrandIdentityReplacementService.class);

    /**
     * 统一的品牌身份名称
     */
    @Value("${whiskerguard.ai.brand.identity:我是猫伯伯智能合规管家}")
    private String brandIdentity;

    /**
     * 是否启用品牌身份替换功能
     */
    @Value("${whiskerguard.ai.brand.replacement.enabled:true}")
    private boolean replacementEnabled;

    /**
     * AI模型身份识别模式列表
     * 包含各种常见的AI模型自我介绍模式
     */
    private List<IdentityPattern> identityPatterns;

    /**
     * 延迟初始化身份模式列表
     * 确保在Spring属性注入完成后再初始化
     */
    private void ensureIdentityPatternsInitialized() {
        if (identityPatterns == null) {
            this.identityPatterns = initializeIdentityPatterns();
        }
    }

    /**
     * 处理AI响应内容，替换模型身份为品牌身份
     * 
     * @param aiResponse 原始AI响应内容
     * @return 处理后的响应内容
     */
    public String processResponse(String aiResponse) {
        if (!replacementEnabled || aiResponse == null || aiResponse.trim().isEmpty()) {
            return aiResponse;
        }

        try {
            // 确保身份模式已初始化
            ensureIdentityPatternsInitialized();

            String processedResponse = aiResponse;
            boolean hasReplacement = false;

            // 遍历所有身份模式进行替换
            for (IdentityPattern pattern : identityPatterns) {
                String beforeReplacement = processedResponse;
                processedResponse = pattern.replace(processedResponse, brandIdentity);
                
                if (!beforeReplacement.equals(processedResponse)) {
                    hasReplacement = true;
                    log.debug("检测到模型身份并已替换: {} -> {}", pattern.getDescription(), brandIdentity);
                }
            }

            if (hasReplacement) {
                log.info("已将AI模型身份替换为品牌身份: {}", brandIdentity);
            }

            return processedResponse;
        } catch (Exception e) {
            log.error("品牌身份替换处理失败，返回原始响应", e);
            return aiResponse;
        }
    }

    /**
     * 初始化身份识别模式列表
     * 
     * @return 身份模式列表
     */
    private List<IdentityPattern> initializeIdentityPatterns() {
        List<IdentityPattern> patterns = new ArrayList<>();

        // Kimi 相关模式
        patterns.add(new IdentityPattern(
            "Kimi身份介绍",
            Pattern.compile("我是\\s*[Kk]imi(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        // 豆包相关模式
        patterns.add(new IdentityPattern(
            "豆包身份介绍",
            Pattern.compile("我是\\s*豆包(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        // DeepSeek 相关模式
        patterns.add(new IdentityPattern(
            "DeepSeek身份介绍",
            Pattern.compile("我是\\s*[Dd]eep[Ss]eek(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        // Claude 相关模式
        patterns.add(new IdentityPattern(
            "Claude身份介绍",
            Pattern.compile("我是\\s*[Cc]laude(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        // ChatGPT 相关模式
        patterns.add(new IdentityPattern(
            "ChatGPT身份介绍",
            Pattern.compile("我是\\s*[Cc]hat[Gg][Pp][Tt](?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        // 通义千问相关模式
        patterns.add(new IdentityPattern(
            "通义千问身份介绍",
            Pattern.compile("我是\\s*通义千问(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        // 文心一言相关模式
        patterns.add(new IdentityPattern(
            "文心一言身份介绍",
            Pattern.compile("我是\\s*文心一言(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        // 通用AI助手模式
        patterns.add(new IdentityPattern(
            "通用AI助手介绍",
            Pattern.compile("我是\\s*一个\\s*AI\\s*助手(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        patterns.add(new IdentityPattern(
            "通用人工智能助手介绍",
            Pattern.compile("我是\\s*一个\\s*人工智能\\s*助手(?:[，,]|\\s|$)", Pattern.CASE_INSENSITIVE)
        ));

        return patterns;
    }

    /**
     * 身份模式类
     * 封装身份识别和替换逻辑
     */
    private static class IdentityPattern {
        private final String description;
        private final Pattern pattern;

        public IdentityPattern(String description, Pattern pattern) {
            this.description = description;
            this.pattern = pattern;
        }

        public String replace(String text, String brandIdentity) {
            return pattern.matcher(text).replaceAll(brandIdentity);
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取当前配置的品牌身份
     * 
     * @return 品牌身份名称
     */
    public String getBrandIdentity() {
        return brandIdentity;
    }

    /**
     * 检查是否启用了品牌身份替换功能
     * 
     * @return 是否启用
     */
    public boolean isReplacementEnabled() {
        return replacementEnabled;
    }
}
